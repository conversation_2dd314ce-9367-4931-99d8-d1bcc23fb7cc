package com.kaolafm.kradio.lib.utils;

import android.content.Context;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.graphics.Color;
import android.os.Build;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import com.kaolafm.kradio.lib.R;
import com.kaolafm.kradio.lib.base.AppDelegate;

import java.lang.reflect.Method;

/**
 * 获取屏幕相关工具类
 * Created by donald on 2017/10/17.
 */

public class ScreenUtil {

    private static Context mContext = AppDelegate.getInstance().getContext().getApplicationContext();

    private ScreenUtil() {
    }

    /**
     * 返回当前屏幕是否为竖屏。
     *
     * @return 当且仅当当前屏幕为竖屏时返回true, 否则返回false。
     */
    public static boolean isPortrait() {
        return mContext.getResources().getConfiguration().orientation == Configuration.ORIENTATION_PORTRAIT;
    }

    /**
     * 获取状态栏高度
     */
    public static int getStatusBarHeight() {
        int result = 0;
        int resourceId = mContext.getResources().getIdentifier("status_bar_height", "dimen", "android");
        if (resourceId > 0) {
            result = mContext.getResources().getDimensionPixelSize(resourceId);
        }
        return result;
    }

    /**
     * 获取屏幕宽度 px
     */
    public static int getScreenWidth() {
        WindowManager wm = (WindowManager) mContext.getSystemService(Context.WINDOW_SERVICE);
        DisplayMetrics displayMetrics = new DisplayMetrics();
        wm.getDefaultDisplay().getRealMetrics(displayMetrics);
        return displayMetrics.widthPixels;
    }

    /**
     * 获取屏幕高度
     */
    public static int getScreenHeight() {
        // 解决横竖屏切换时，偶现的 获取屏幕高度 错乱问题,在横版情况下，高度低于宽度,返回 宽高度中最小值
        WindowManager wm = (WindowManager) mContext.getSystemService(Context.WINDOW_SERVICE);
        DisplayMetrics displayMetrics = new DisplayMetrics();
        wm.getDefaultDisplay().getRealMetrics(displayMetrics);
        // 添加log,查看一个 偶现问题。
        // Log.i("heightPixels", " " + displayMetrics.heightPixels);
//        if (displayMetrics.heightPixels >= displayMetrics.widthPixels) {
//            return displayMetrics.widthPixels;
//        } else {
//            return displayMetrics.heightPixels;
//        }
        return displayMetrics.heightPixels;
    }

    public static String getScreeSize() {
        return getScreenWidth() + "*" + getScreenHeight();
    }

    /**
     * 获取虚拟按键的高度
     */
    public static int getNavigationBarHeight() {
        if (!checkDeviceHasNavigationBar()) {
            return 0;
        }
        int navigationBarHeight = 0;
        Resources resources = mContext.getResources();
        int resourceId = resources.getIdentifier("navigation_bar_height", "dimen", "android");
        if (resourceId > 0) {
            navigationBarHeight = resources.getDimensionPixelSize(resourceId);
        }
        return navigationBarHeight;
    }

    /**
     * 检查是否有虚拟按键
     */
    public static boolean checkDeviceHasNavigationBar() {
        if (Build.VERSION.SDK_INT < 21) {
            return false;
        }
        boolean hasNavigationBar = false;
        Resources resources = mContext.getResources();
        int id = resources.getIdentifier("config_showNavigationBar", "bool", "android");
        if (id > 0) {
            hasNavigationBar = resources.getBoolean(id);
        }
        //检查虚拟按键是否被重写
        try {
            Class systemProperties = Class.forName("android.os.SystemProperties");
            Method getMethod = systemProperties.getMethod("get", String.class);
            String navBarOverride = (String) getMethod.invoke(systemProperties, "qemu.hw.mainkeys");
            if (TextUtils.equals("1", navBarOverride)) {
                hasNavigationBar = false;
            } else if (TextUtils.equals("0", navBarOverride)) {
                hasNavigationBar = true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return hasNavigationBar;
    }

    /**
     * 除去status高度的屏幕高度
     */
    public static int getScreenHeightWithoutStatus() {
        return getScreenHeight() - getStatusBarHeight();
    }


    /**
     * dp 转px
     */
    public static int dp2px(int dp) {
        final float scale = mContext.getResources().getDisplayMetrics().density;
        return (int) (dp * scale + 0.5f);
    }

    public static int px2dp(int px) {
        final float scale = mContext.getResources().getDisplayMetrics().density;
        return (int) (px / scale + 0.5f);
    }

    public static int sp2px(float sp) {
        final float scale = mContext.getResources().getDisplayMetrics().scaledDensity;
        return (int) (sp * scale + 0.5f);
    }

    public static int px2sp(float pxValue) {
        final float fontScale = mContext.getResources().getDisplayMetrics().scaledDensity;
        return (int) (pxValue / fontScale + 0.5f);
    }

    public static void setStatusBar(Window window, boolean navi) {
        setStatusBar(window, navi, true);
    }

    public static void setStatusBar(Window window, boolean navi, boolean isStatusBarTransparent) {
        //api>21,全透明状态栏和导航栏;api>19,半透明状态栏和导航栏
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS
                    | WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION);
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            if (isStatusBarTransparent) {
                window.setStatusBarColor(Color.TRANSPARENT);
            }
            if (navi) {
                window.getDecorView().setSystemUiVisibility(
                        //状态栏不会被隐藏但activity布局会扩展到状态栏所在位置
                        View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                                //导航栏不会被隐藏但activity布局会扩展到导航栏所在位置
                                | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                                | View.SYSTEM_UI_FLAG_LAYOUT_STABLE);
                window.setNavigationBarColor(Color.TRANSPARENT);
            } else {
                window.getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                        | View.SYSTEM_UI_FLAG_LAYOUT_STABLE);
            }
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            if (navi) {
                //半透明导航栏
                window.addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION);
            }
            //半透明状态栏
            window.addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
        }
    }


    /**
     * 获取全局页面左边距
     *
     * @param orientation Configuration.ORIENTATION_LANDSCAPE or Configuration.ORIENTATION_PORTRAIT
     * @return
     */
    public static int getGlobalPaddingLeft(int orientation) {
        int padding;
        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
//            padding = ResUtil.getDimen(R.dimen.m50) + ResUtil.getDimen(R.dimen.m22);
            padding = ResUtil.getDimen(R.dimen.m70);
        } else {
//            padding = ResUtil.getDimen(R.dimen.m25) + ResUtil.getDimen(R.dimen.m22);
            padding = ResUtil.getDimen(R.dimen.x50);
        }
        return padding;
    }

    /**
     * 获取全局页面右边距
     *
     * @param orientation Configuration.ORIENTATION_LANDSCAPE or Configuration.ORIENTATION_PORTRAIT
     * @return
     */
    public static int getGlobalPaddingRight(int orientation) {
        int padding;
        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
//            padding = ResUtil.getDimen(R.dimen.m50) + ResUtil.getDimen(R.dimen.m22);
            padding = ResUtil.getDimen(R.dimen.m80);
        } else {
//            padding = ResUtil.getDimen(R.dimen.m25) + ResUtil.getDimen(R.dimen.m22);
            padding = ResUtil.getDimen(R.dimen.x50);
        }
        return padding;
    }

    /**
     * 获取内容区域相对于全屏的比例
     * <p>
     * <p>或使用常量:com.kaolafm.view.ViewConstants.TITLE_LAND_PERCENT 或 com.kaolafm.view.ViewConstants.TITLE_PORT_PERCENT
     *
     * @param orientation 屏幕方向
     * @return
     */
    public static float getGlobalConstrainPercentWidth(int orientation) {
        float bias = ((float) getScreenWidth() - getGlobalPaddingRight(orientation) - getGlobalPaddingLeft(orientation)) / getScreenWidth();
        return bias;
    }

    /**
     * 计算返回键控件的左边距.箭头中线与内容左边距对齐
     *
     * @param backView    返回键控件
     * @param orientation 屏幕方向
     * @return
     */
    public static int getGlobalBackMarginLeft(View backView, int orientation) {
        //计算箭头的尖儿,距离bbfBack左边的距离
        int width = backView.getWidth();
        if (width == 0) {
            width = ResUtil.getDimen(R.dimen.m80);
        }
        int srcWidht = width - backView.getPaddingLeft() - backView.getPaddingRight();
        double srcRectW = srcWidht / 2 / 1.414;
        double arrawLeft = width / 2;// width / 2 - srcRectW;

        int leftMargin = (int) (getGlobalPaddingLeft(orientation));
//        int paddingRight = ScreenUtil.getGlobalPaddingRight(orientation);
        return leftMargin;
    }

    /**
     * 全局通知栏宽度
     *
     * @return
     */
    public static int getGlobalNotifyWindowWidth(int orientation) {
        return getScreenWidth() - getGlobalPaddingRight(orientation) - getGlobalPaddingLeft(orientation);
    }
}
